import logging
import re
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

from disposition import Disposition
from scrapers.rental_offer import RentalOffer
from scrapers.scraper_base import ScraperBase


class ScraperBravis(ScraperBase):

    name = "BRAVIS"
    logo_url = "https://www.bravis.cz/content/img/logo-small.png"
    color = 0xCE0020
    base_url = "https://www.bravis.cz/pronajem-bytu"


    def build_response(self) -> requests.Response:
        url = self.base_url + "?"

        if Disposition.FLAT_1KK in self.disposition or Disposition.FLAT_1 in self.disposition:
            url += "typ-nemovitosti-byt+1=&"
        if Disposition.FLAT_2KK in self.disposition or Disposition.FLAT_2 in self.disposition:
            url += "typ-nemovitosti-byt+2=&"
        if Disposition.FLAT_3KK in self.disposition or Disposition.FLAT_3 in self.disposition:
            url += "typ-nemovitosti-byt+3=&"
        if Disposition.FLAT_4KK in self.disposition or Disposition.FLAT_4 in self.disposition:
            url += "typ-nemovitosti-byt+4=&"
        if Disposition.FLAT_5_UP in self.disposition:
            url += "typ-nemovitosti-byt+5=&"

        url += "typ-nabidky=pronajem-bytu&lokalita=cele-brno&vybavenost=nezalezi&q=&action=search&s=1-20-order-0"

        logging.debug("BRAVIS request: %s", url)

        return requests.get(url, headers=self.headers)

    def get_latest_offers(self) -> list[RentalOffer]:
        response = self.build_response()
        soup = BeautifulSoup(response.text, 'html.parser')

        items: list[RentalOffer] = []

        # Nová struktura: .itemslist > .initemslist > .item
        for item in soup.select(".itemslist .initemslist .item"):
            try:
                # Hlavní odkaz je první (a jediný) <a> tag v .item
                main_link = item.select_one("a")
                if not main_link:
                    continue

                # Název je v h1 uvnitř .desc
                title_element = item.select_one(".desc h1")
                if not title_element:
                    continue
                title = title_element.get_text().strip()

                # Lokace je v elementu s třídou .location uvnitř .desc
                location_element = item.select_one(".desc .location")
                if not location_element:
                    continue
                location = location_element.get_text().strip()

                # Cena je v .price uvnitř .desc
                price_element = item.select_one(".desc .price")
                if not price_element:
                    continue

                # Extrakce číselné hodnoty z ceny
                price_text = [text for text in price_element.stripped_strings][0]
                price = int(re.sub(r"[^\d]", "", price_text))

                # Obrázek je v img uvnitř .image
                img_element = item.select_one(".image img")
                if not img_element:
                    continue
                image_url = urljoin(self.base_url, img_element.get("src"))

                items.append(RentalOffer(
                    scraper = self,
                    link = urljoin(self.base_url, main_link.get("href")),
                    title = title,
                    location = location,
                    price = price,
                    image_url = image_url
                ))

            except Exception as e:
                logging.warning(f"Chyba při parsování nabídky z Bravis: {e}")
                continue

        return items
