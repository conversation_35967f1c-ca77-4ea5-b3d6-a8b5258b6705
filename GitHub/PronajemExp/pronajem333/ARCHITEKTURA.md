# Architektura aplikace PronajemExp

## Přehled
Aplikace PronajemExp je Discord bot napsaný v <PERSON>u, který automaticky vyhledává a monitoruje nabídky pronájmu bytů z různých realitních portálů a odesílá nové nabídky do Discord kanálu.

## Technologie
- **Python 3.x** - hlavní programovací jazyk
- **Discord.py** - knihovna pro Discord bot API
- **Requests** - HTTP klient pro web scraping
- **BeautifulSoup4** - parsing HTML obsahu
- **python-dotenv** - správa environment proměnných
- **environ-config** - konfigurace aplikace

## Struktura projektu

### Hlavní soubory
- `main.py` - vstupní bod aplikace, Discord bot logika
- `config.py` - konfigurace aplikace z environment proměnných
- `scrapers_manager.py` - správa všech scraperů
- `offers_storage.py` - ukládán<PERSON> a detekce duplikátů nabídek
- `disposition.py` - definice typů dispozic bytů
- `discord_logger.py` - logování chyb do Discord kanálu
- `utils.py` - pomocné funkce

### Adresář scrapers/
- `scraper_base.py` - abstraktní třída pro všechny scrapery
- `rental_offer.py` - datová třída pro nabídku pronájmu
- `scraper_*.py` - implementace scraperů pro jednotlivé portály:
  - Sreality
  - Bezrealitky
  - Bravis
  - Euro Bydlení
  - iDNES Reality
  - Realcity
  - Realingo
  - RE/MAX
  - Ulov Domov

### Ostatní adresáře
- `logs/` - log soubory aplikace
- `graphql/` - GraphQL konfigurace pro některé scrapery
- `.local/` - lokální Python balíčky

## Architektura

### 1. Discord Bot (main.py)
- Používá Discord.py s app_commands pro slash příkazy
- Periodicky spouští scraping pomocí `@tasks.loop`
- Filtruje nabídky podle ceny (max 14000 Kč)
- Detekuje duplikáty pomocí hash funkcí
- Odesílá nové nabídky jako Discord embedy

### 2. Scraper systém
**ScraperBase** - abstraktní třída definující:
- `name` - název portálu
- `logo_url` - URL loga portálu
- `color` - barva pro Discord embed
- `disposition_mapping` - mapování dispozic
- `build_response()` - vytvoření HTTP požadavku
- `get_latest_offers()` - získání nabídek

**RentalOffer** - datová třída obsahující:
- `link` - URL nabídky
- `title` - název nabídky
- `location` - lokalita
- `price` - cena
- `image_url` - URL obrázku
- `scraper` - reference na scraper
- `unique_hash` - hash pro detekci duplikátů

### 3. Konfigurace
Aplikace používá environment proměnné:
- `DISCORD_TOKEN` - token Discord bota
- `DISCORD_OFFERS_CHANNEL` - ID kanálu pro nabídky
- `DISCORD_DEV_CHANNEL` - ID kanálu pro chyby
- `DEBUG` - debug režim
- `FOUND_OFFERS_FILE` - soubor pro ukládání hashů
- `REFRESH_INTERVAL_DAYTIME_MINUTES` - interval během dne
- `REFRESH_INTERVAL_NIGHTTIME_MINUTES` - interval v noci
- `DISPOSITIONS` - požadované dispozice (1+kk,2+1,...)

### 4. Ukládání dat
- `OffersStorage` - ukládá hashe nabídek do textového souboru
- Detekce duplikátů pomocí MD5 hash z title+price+location
- `found_offers.txt` - perzistentní úložiště hashů

### 5. Logování
- Standardní Python logging
- `DiscordLogger` - odesílá chyby do Discord kanálu
- Log soubory v adresáři `logs/`

## Workflow aplikace

1. **Inicializace**
   - Načtení konfigurace z .env souborů
   - Vytvoření scraperů pro všechny portály
   - Připojení k Discord API

2. **Periodické spouštění**
   - Každých X minut (podle denní/noční doby)
   - Spuštění všech scraperů paralelně
   - Získání nových nabídek

3. **Zpracování nabídek**
   - Filtrování podle ceny (max 14000 Kč)
   - Detekce duplikátů pomocí hashů
   - Uložení nových hashů

4. **Odesílání do Discord**
   - Vytvoření Discord embedů
   - Odesílání do nakonfigurovaného kanálu
   - Logování chyb

## Podporované portály
1. **Sreality.cz** - největší český realitní portál
2. **Bezrealitky.cz** - portál bez realitních kanceláří
3. **Bravis.cz** - realitní kancelář
4. **Euro Bydlení** - realitní portál
5. **iDNES Reality** - realitní sekce iDNES
6. **Realcity.cz** - realitní portál
7. **Realingo.cz** - moderní realitní platforma
8. **RE/MAX** - mezinárodní realitní síť
9. **Ulov Domov** - realitní portál

## Dispozice bytů
Aplikace podporuje následující dispozice:
- 1+kk, 1+1 (garsonky a malé byty)
- 2+kk, 2+1 (dvoupokojové byty)
- 3+kk, 3+1 (třípokojové byty)
- 4+kk, 4+1 (čtyřpokojové byty)
- 5++ (pět a více pokojů)
- others (ostatní typy)

## Bezpečnost a limity
- Filtrování podle maximální ceny
- Rate limiting pro web scraping
- Error handling pro nedostupné portály
- Detekce duplikátů
- Logování všech chyb

## Deployment
Aplikace je navržena pro běh v kontejneru nebo na serveru s:
- Python 3.x runtime
- Přístupem k internetu
- Discord bot tokenem
- Nakonfigurovanými environment proměnnými
